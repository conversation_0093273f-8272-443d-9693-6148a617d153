#!/usr/bin/env python
# encoding:utf-8

"""
奇安信天眼告警服务 MCP Server
基于 Model Context Protocol (MCP) 的奇安信天眼告警查询工具集成
提供主要功能：
1. 查询指定时间范围内的告警信息
"""

from mcp.server.fastmcp import FastMCP
import logging
import requests
import time
import datetime
import random
import re
import base64
from typing import Dict, Optional, Any, List
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 尝试导入验证码识别库
try:
    import ddddocr
    HAS_DDDDOCR = True
except ImportError:
    HAS_DDDDOCR = False
    logger.warning("ddddocr模块未安装，验证码识别功能将不可用")

# 创建 FastMCP 实例
mcp = FastMCP("奇安信天眼告警服务")


class QianXinLoginSimulator:
    """奇安信天眼登录模拟器"""

    def __init__(self, base_url):
        """
        初始化登录模拟器

        Args:
            base_url (str): 服务器地址
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.csrf_token = None
        self.session_cookie = None

        # 禁用SSL证书验证
        self.session.verify = False

        # 设置通用请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'sec-ch-ua': '"Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        })

        # 初始化验证码识别器
        if HAS_DDDDOCR:
            self.ocr = ddddocr.DdddOcr()
        else:
            self.ocr = None

    def step1_get_login_page(self):
        """步骤1: 获取登录页面，获取初始session和csrf_token"""
        logger.info("步骤1: 获取登录页面...")

        url = f"{self.base_url}/sensor/login"
        headers = {
            'Cache-Control': 'max-age=0',
            'Upgrade-Insecure-Requests': '1',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-User': '?1',
            'Sec-Fetch-Dest': 'document',
            'Referer': f'{self.base_url}/sensor/home/<USER>'
        }

        response = self.session.get(url, headers=headers, verify=False)
        logger.info(f"状态码: {response.status_code}")

        # 从响应中提取csrf_token
        csrf_match = re.search(r'content="([a-f0-9]{32})"', response.text)
        if csrf_match:
            self.csrf_token = csrf_match.group(1)
            logger.info(f"获取到 CSRF Token: {self.csrf_token}")

        # 获取session cookie
        if 'session-sensor' in response.cookies:
            self.session_cookie = response.cookies['session-sensor']
            logger.info(f"获取到 Session Cookie: {self.session_cookie}")

        return response.status_code == 200

    def step2_get_project_config(self):
        """步骤2: 获取项目配置文件"""
        logger.info("步骤2: 获取项目配置...")

        timestamp = int(time.time() * 1000)
        url = f"{self.base_url}/sensor/project/project.config.json?v={timestamp}"
        headers = {
            'Accept': '*/*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }

        response = self.session.get(url, headers=headers, verify=False)
        logger.info(f"状态码: {response.status_code}")

        if response.status_code == 200:
            try:
                config = response.json()
                logger.info(f"项目版本: {config.get('version', 'Unknown')}")
                return True
            except:
                logger.warning("配置文件解析失败")
                return False
        return False

    def step3_get_favicon(self):
        """步骤3: 获取网站图标"""
        logger.info("步骤3: 获取网站图标...")

        url = f"{self.base_url}/static/client/favicon.ico"
        headers = {
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'no-cors',
            'Sec-Fetch-Dest': 'image',
            'Referer': f'{self.base_url}/sensor/login'
        }

        response = self.session.get(url, headers=headers, verify=False)
        logger.info(f"状态码: {response.status_code}")
        return response.status_code == 200

    def step4_check_login_status(self):
        """步骤4: 检查登录状态"""
        logger.info("步骤4: 检查登录状态...")

        random_param = random.random()
        url = f"{self.base_url}/skyeye/admin/login?csrf_token={self.csrf_token}&r={random_param}"
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }

        response = self.session.get(url, headers=headers, verify=False)
        logger.info(f"状态码: {response.status_code}")

        if response.status_code == 200:
            try:
                data = response.json()
                logger.info(f"登录检查结果: {data.get('message', 'Unknown')}")
                return data.get('status') == 400  # 400表示未登录，这是正常的
            except:
                return False
        return False

    def step5_get_sitemap(self):
        """步骤5: 获取站点地图"""
        logger.info("步骤5: 获取站点地图...")

        timestamp = int(time.time() * 1000)
        url = f"{self.base_url}/sensor/project/sitemap.json?v={timestamp}"
        headers = {
            'Accept': '*/*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }

        response = self.session.get(url, headers=headers, verify=False)
        logger.info(f"状态码: {response.status_code}")
        return response.status_code == 200

    def step6_check_updating_status(self):
        """步骤6: 检查系统更新状态"""
        logger.info("步骤6: 检查系统更新状态...")

        random_param = random.random()
        url = f"{self.base_url}/skyeye/config/is_updating?csrf_token={self.csrf_token}&r={random_param}"
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }

        response = self.session.get(url, headers=headers, verify=False)
        logger.info(f"状态码: {response.status_code}")
        return response.status_code == 200

    def step7_switch_language(self):
        """步骤7: 切换语言设置"""
        logger.info("步骤7: 切换语言设置...")

        random_param = random.random()
        url = f"{self.base_url}/skyeye/state/switch/language?csrf_token={self.csrf_token}&r={random_param}"
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }

        response = self.session.get(url, headers=headers, verify=False)
        logger.info(f"状态码: {response.status_code}")
        return response.status_code == 200

    def step8_get_login_microapp(self):
        """步骤8: 获取登录微应用"""
        logger.info("步骤8: 获取登录微应用...")

        url = f"{self.base_url}/sensor/microapps/login-sensor/index.html?v=0.21.0-rc.0"
        headers = {
            'Accept': '*/*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }

        response = self.session.get(url, headers=headers, verify=False)
        logger.info(f"状态码: {response.status_code}")
        return response.status_code == 200

    def step9_get_logo(self):
        """步骤9: 获取系统Logo"""
        logger.info("步骤9: 获取系统Logo...")

        url = f"{self.base_url}/skyeye/logo/skyeye-logo-big2.png"
        headers = {
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'no-cors',
            'Sec-Fetch-Dest': 'image',
            'Referer': f'{self.base_url}/sensor/login',
            'If-None-Match': '"64bf3e20-132e"',
            'If-Modified-Since': 'Tue, 25 Jul 2023 03:14:40 GMT'
        }

        response = self.session.get(url, headers=headers, verify=False)
        logger.info(f"状态码: {response.status_code}")
        return response.status_code in [200, 304]

    def step10_get_license_info(self):
        """步骤10: 获取许可证信息"""
        logger.info("步骤10: 获取许可证信息...")

        random_param = random.random()
        url = f"{self.base_url}/skyeye/config/unlogin_license?csrf_token={self.csrf_token}&r={random_param}"
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }

        response = self.session.get(url, headers=headers, verify=False)
        logger.info(f"状态码: {response.status_code}")
        return response.status_code == 200

    def step11_check_agency_status(self):
        """步骤11: 检查代理状态"""
        logger.info("步骤11: 检查代理状态...")

        random_param = random.random()
        url = f"{self.base_url}/skyeye/config/is_agency?csrf_token={self.csrf_token}&r={random_param}"
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }

        response = self.session.get(url, headers=headers, verify=False)
        logger.info(f"状态码: {response.status_code}")
        return response.status_code == 200

    def step12_get_captcha(self, auto_recognize=True):
        """步骤12: 获取验证码"""
        logger.info("步骤12: 获取验证码...")

        random_param = random.random()
        url = f"{self.base_url}/skyeye/admin/code?r={random_param}"
        headers = {
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'no-cors',
            'Sec-Fetch-Dest': 'image',
            'Referer': f'{self.base_url}/sensor/login'
        }

        response = self.session.get(url, headers=headers, verify=False)
        logger.info(f"状态码: {response.status_code}")

        if response.status_code == 200:
            # 自动识别验证码
            if auto_recognize:
                try:
                    logger.info("正在自动识别验证码...")
                    if self.ocr:
                        result = self.ocr.classification(response.content)
                        logger.info(f"自动识别结果: {result}")
                        return True, result
                    else:
                        # 使用默认验证码
                        result = "1234"
                        logger.warning(f"使用默认验证码: {result}")
                        return True, result
                except Exception as e:
                    logger.error(f"自动识别失败: {e}")
                    return True, "1234"
            else:
                return True, None

        return False, None

    def encrypt_password(self):
        """加密密码 (模拟前端加密逻辑)"""
        return "U2FsdGVkX18woJynzevdp9AF+c6KpWiR0H+dfkYJfDo="

    def step13_login(self, username, password, captcha):
        """步骤13: 执行登录"""
        logger.info("步骤13: 执行登录...")

        # 加密密码
        encrypted_password = self.encrypt_password()

        url = f"{self.base_url}/skyeye/admin/login"
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'Origin': self.base_url,
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }

        login_data = {
            "type_login": "sys",
            "username": username,
            "password": encrypted_password,
            "authcode": captcha,
            "csrf_token": self.csrf_token,
            "r": random.random()
        }

        response = self.session.post(url, headers=headers, json=login_data, verify=False)
        logger.info(f"状态码: {response.status_code}")

        if response.status_code == 200:
            try:
                result = response.json()
                logger.info(f"登录结果: {result.get('message', 'Unknown')}")

                if result.get('status') == 200:
                    logger.info("登录成功!")
                    logger.info(f"用户信息: {result.get('data', {})}")
                    return True
                else:
                    logger.error(f"登录失败: {result.get('message', 'Unknown error')}")
                    return False
            except Exception as e:
                logger.error(f"响应解析失败: {e}")
                return False

        return False

    def _login_once(self, username, password, auto_recognize=True, verbose=False):
        """
        执行一次完整的登录流程（内部方法，不包含重试逻辑）

        Args:
            username (str): 用户名
            password (str): 密码
            auto_recognize (bool): 是否自动识别验证码，默认True
            verbose (bool): 是否显示详细日志，默认False

        Returns:
            tuple: (success: bool, error_msg: str) 登录是否成功和错误信息
        """
        try:
            # 执行前11个步骤
            steps = [
                self.step1_get_login_page,
                self.step2_get_project_config,
                self.step3_get_favicon,
                self.step4_check_login_status,
                self.step5_get_sitemap,
                self.step6_check_updating_status,
                self.step7_switch_language,
                self.step8_get_login_microapp,
                self.step9_get_logo,
                self.step10_get_license_info,
                self.step11_check_agency_status
            ]

            # 执行前置步骤
            for i, step in enumerate(steps, 1):
                try:
                    success = step()
                    if not success and verbose:
                        logger.warning(f"步骤 {i} 执行失败，但继续执行...")
                    # 如果步骤1失败，直接返回失败
                    if i == 1 and not success:
                        return False, "获取登录页面失败"
                    time.sleep(0.1)  # 模拟真实的请求间隔
                except Exception as e:
                    if verbose:
                        logger.warning(f"步骤 {i} 执行异常: {e}")
                    # 如果关键步骤失败，直接返回失败
                    if i == 1:  # 步骤1是获取登录页面，这是关键步骤
                        return False, f"关键步骤异常: {e}"

            # 步骤12: 获取并识别验证码
            captcha_success, auto_captcha = self.step12_get_captcha(auto_recognize)

            if not captcha_success:
                return False, "获取验证码失败"

            # 确定最终使用的验证码
            final_captcha = auto_captcha

            if not final_captcha:
                return False, "验证码识别失败"

            # 执行登录
            login_success = self.step13_login(username, password, final_captcha)

            if login_success:
                if verbose:
                    logger.info("登录成功!")
                return True, "登录成功"
            else:
                return False, "登录失败，可能是验证码错误或用户名密码错误"

        except Exception as e:
            return False, f"登录过程发生异常: {e}"

    def login(self, username, password, auto_recognize=True, verbose=False, max_retries=5):
        """
        带重试机制的登录函数

        Args:
            username (str): 用户名
            password (str): 密码
            auto_recognize (bool): 是否自动识别验证码，默认True
            verbose (bool): 是否显示详细日志，默认False
            max_retries (int): 最大重试次数，默认5次

        Returns:
            bool: 登录是否成功
        """
        if verbose:
            logger.info("=" * 80)
            logger.info("开始登录奇安信流量传感器（带重试机制）")
            logger.info(f"最大重试次数: {max_retries}")
            logger.info("=" * 80)

        for attempt in range(max_retries):
            if verbose:
                if attempt == 0:
                    logger.info(f"第 {attempt + 1} 次尝试登录...")
                else:
                    logger.info(f"\n第 {attempt + 1} 次尝试登录...")

            # 执行一次完整的登录流程
            success, error_msg = self._login_once(username, password, auto_recognize, verbose)

            if success:
                if verbose:
                    logger.info("✅ 登录成功!")
                return True
            else:
                if verbose:
                    logger.warning(f"❌ 登录失败: {error_msg}")
                    if attempt < max_retries - 1:
                        logger.info(f"等待2秒后重试... (剩余重试次数: {max_retries - attempt - 1})")
                    else:
                        logger.warning("已达到最大重试次数")

                # 如果不是最后一次尝试，等待后重试
                if attempt < max_retries - 1:
                    time.sleep(2)
                else:
                    if verbose:
                        logger.error("=" * 80)
                        logger.error("❌ 登录失败，已达到最大重试次数")
                        logger.error("=" * 80)
                    return False

        return False

    def api_request(self, method, endpoint, **kwargs):
        """
        发送API请求的统一方法

        Args:
            method (str): HTTP方法 (GET, POST, PUT, DELETE等)
            endpoint (str): API端点路径，如 "/skyeye/alarm/alert_list"
            **kwargs: 其他请求参数，如 params, json, data, headers等

        Returns:
            requests.Response: HTTP响应对象
        """
        # 构建完整URL
        if endpoint.startswith('/'):
            url = f"{self.base_url}{endpoint}"
        else:
            url = f"{self.base_url}/{endpoint}"

        # 设置默认headers
        default_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty'
        }

        # 合并headers
        headers = kwargs.get('headers', {})
        headers.update(default_headers)
        kwargs['headers'] = headers

        # 添加CSRF token到参数中（如果需要）
        if self.csrf_token and method.upper() in ['POST', 'PUT', 'DELETE']:
            if 'json' in kwargs:
                if isinstance(kwargs['json'], dict):
                    kwargs['json']['csrf_token'] = self.csrf_token
            elif 'data' in kwargs:
                if isinstance(kwargs['data'], dict):
                    kwargs['data']['csrf_token'] = self.csrf_token

        # 禁用SSL验证
        kwargs['verify'] = False

        # 发送请求
        return self.session.request(method, url, **kwargs)

    def get_session(self):
        """获取已登录的session"""
        return self.session


class AlertQueryManager:
    """告警查询管理器"""

    def __init__(self, login_simulator):
        """
        初始化查询管理器

        Args:
            login_simulator (QianXinLoginSimulator): 已登录的模拟器实例
        """
        self.simulator = login_simulator

    def get_alerts_by_minutes_range(self, minutes_ago=5, limit=50):
        """
        根据时间范围查询告警

        Args:
            minutes_ago (int): 查询多少分钟前的告警
            limit (int): 返回数量限制

        Returns:
            dict: 告警数据
        """
        # 确保参数类型正确
        try:
            minutes_ago = int(minutes_ago) if minutes_ago is not None else 5
            limit = int(limit) if limit is not None else 50
        except (ValueError, TypeError):
            logger.error(f"参数类型错误: minutes_ago={minutes_ago}, limit={limit}")
            return None

        current_time = int(time.time() * 1000)  # 毫秒时间戳
        start_time = current_time - (minutes_ago * 60 * 1000)  # 分钟转毫秒

        # 构建查询参数
        params = {
            'offset': 1,
            'limit': limit,
            'order_by': 'access_time:desc',
            'is_accurate': 0,
            'data_source': 1,
            'start_time': start_time,
            'end_time': current_time,
            'csrf_token': self.simulator.csrf_token,
            'r': random.random()
        }

        # 添加默认的空参数（保持与原始请求一致）
        default_filters = {
            'host_state': '',
            'status': '',
            'ioc': '',
            'threat_name': '',
            'attack_stage': '',
            'x_forwarded_for': '',
            'host': '',
            'status_http': '',
            'alarm_source': '',
            'uri': '',
            'attck_org': '',
            'attck': '',
            'alert_rule': '',
            'attack_dimension': '',
            'is_web_attack': '',
            'sip': '',
            'dip': '',
            'sport': '',
            'dport': '',
            'dst_mac': '',
            'src_mac': '',
            'vlan_id': '',
            'marks': '',
            'vxlan_id': '',
            'start_update_time': '',
            'end_update_time': '',
            'threat_type': '',
            'hazard_level': '',
            'alarm_sip': '',
            'attack_sip': '',
            'alarm_id': '',
            'file_name': '',
            'file_md5': '',
            'file_type': '',
            'proto': '',
            'user_label': ''
        }

        # 合并参数
        params.update(default_filters)

        try:
            # 使用模拟器的api_request方法
            response = self.simulator.api_request('GET', '/skyeye/alarm/alert_list', params=params)

            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取告警列表失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"获取告警列表异常: {e}")
            return None







# 全局变量用于缓存登录状态
_login_manager = None
_alert_manager = None
_last_login_time = None
LOGIN_CACHE_DURATION = 3600  # 登录缓存1小时


def get_authenticated_managers(base_url, username, password):
    """
    获取已认证的管理器实例，如果需要则重新登录

    Args:
        base_url (str): 服务器地址
        username (str): 用户名
        password (str): 密码

    Returns:
        tuple: (login_manager, alert_manager, success)
    """
    global _login_manager, _alert_manager, _last_login_time

    current_time = time.time()

    # 检查是否需要重新登录
    if (_login_manager is None or _alert_manager is None or
        _last_login_time is None or
        current_time - _last_login_time > LOGIN_CACHE_DURATION):

        logger.info("需要重新登录天眼系统")

        # 创建登录管理器
        login_manager = QianXinLoginSimulator(base_url)
        success = login_manager.login(username, password, auto_recognize=True, verbose=False, max_retries=5)

        if not success:
            logger.error("登录失败，无法继续后续操作")
            return None, None, False

        # 创建告警查询管理器
        alert_manager = AlertQueryManager(login_manager)

        # 更新全局缓存
        _login_manager = login_manager
        _alert_manager = alert_manager
        _last_login_time = current_time

        logger.info("登录成功，管理器已缓存")

    return _login_manager, _alert_manager, True


def unix_timestamp_to_beijing_time(timestamp):
    """
    将unix时间戳转换为北京时间格式

    Args:
        timestamp (int): unix时间戳（秒或毫秒）

    Returns:
        str: 格式化的北京时间字符串，如"7月21日11:30"
    """
    try:
        # 判断是秒还是毫秒时间戳
        if timestamp > 10**10:  # 毫秒时间戳
            timestamp = timestamp / 1000

        # 转换为北京时间 (UTC+8)
        dt = datetime.datetime.fromtimestamp(timestamp, tz=datetime.timezone(datetime.timedelta(hours=8)))

        # 格式化为"x月x日xx:xx"
        return dt.strftime("%m月%d日%H:%M").lstrip('0').replace('月0', '月')
    except Exception as e:
        logger.error(f"时间转换失败: {e}")
        return str(timestamp)


def map_table_name_to_chinese(table_name):
    """
    将告警类型映射为中文

    Args:
        table_name (str): 原始表名

    Returns:
        str: 中文告警类型
    """
    mapping = {
        'webids_alert': '网页漏洞利用',
        'ips_alert': '网络攻击'
    }
    return mapping.get(table_name, '其他攻击')


def format_alert_data(alerts_data):
    """
    格式化告警数据，只返回重要告警（攻击结果为"成功"或"失陷"，或威胁等级为"危急"）

    Args:
        alerts_data (dict): 原始告警数据

    Returns:
        dict: 格式化后的告警数据
    """
    if not alerts_data or 'items' not in alerts_data:
        return {'total': 0, 'alerts': []}

    formatted_alerts = []

    for item in alerts_data['items']:
        try:
            # 提取所需字段
            access_time = item.get('access_time')
            alarm_sip = item.get('alarm_sip', item.get('dip', 'Unknown'))  # 受害IP (目标IP)
            attack_sip = item.get('attack_sip', item.get('sip', 'Unknown'))  # 攻击IP (源IP)
            table_name = item.get('table_name', 'unknown')
            rule_name = item.get('rule_name', item.get('threat_name', 'Unknown'))
            host_state = item.get('host_state', 'Unknown')  # 攻击结果
            hazard_level = item.get('hazard_level', 'Unknown')  # 威胁级别
            repeat_count = item.get('repeat_count', item.get('count', 1))  # 次数

            # 只返回重要告警
            if host_state not in ["成功", "失陷"] and hazard_level != "危急":
                continue

            # 格式化数据
            formatted_alert = {
                'access_time': unix_timestamp_to_beijing_time(access_time) if access_time else 'Unknown',  # 最近发生时间
                'alarm_sip': alarm_sip,  # 受害IP
                'attack_sip': attack_sip,  # 攻击IP
                'table_name': map_table_name_to_chinese(table_name),  # 告警类型
                'rule_name': rule_name,  # 威胁名称
                'host_state': host_state,  # 攻击结果
                'hazard_level': hazard_level,  # 威胁级别
                'repeat_count': repeat_count  # 次数
            }

            formatted_alerts.append(formatted_alert)

        except Exception as e:
            logger.error(f"格式化告警数据失败: {e}")
            continue

    return {
        'total': len(formatted_alerts),
        'alerts': formatted_alerts
    }


@mcp.tool()
def query_tianyan_alerts(
    minutes_ago: Optional[int] = 5,
    limit: Optional[int] = 50
) -> Dict[str, Any]:
    """查询奇安信天眼平台的告警信息。

    Args:
        minutes_ago: 查询多少分钟前的告警，默认为5分钟
        limit: 返回告警数量限制，默认为50条

    Returns:
        Dict[str, Any]: 包含告警信息的字典
            - status: "success" 或 "error"
            - data: 格式化的告警列表（成功时）
            - message: 错误信息（失败时）
            - total_alerts: 告警总数（成功时）
    """
    # 固定配置
    base_url = "https://10.228.16.244"
    username = "admin"
    password = "passwd"  # 实际使用加密后的密码

    logger.info(f"[MCP工具] 开始查询天眼告警，参数: minutes_ago={minutes_ago}, limit={limit}")

    try:
        # 确保参数类型正确
        try:
            minutes_ago = int(minutes_ago) if minutes_ago is not None else 5
            limit = int(limit) if limit is not None else 50
        except (ValueError, TypeError) as e:
            return {
                "status": "error",
                "message": f"参数类型错误: minutes_ago和limit必须是数字类型"
            }

        # 获取已认证的管理器
        _, alert_manager, success = get_authenticated_managers(base_url, username, password)

        if not success:
            return {
                "status": "error",
                "message": "登录天眼系统失败"
            }

        # 查询告警
        alerts_data = alert_manager.get_alerts_by_minutes_range(
            minutes_ago=minutes_ago,
            limit=limit
        )

        if not alerts_data:
            return {
                "status": "error",
                "message": "查询告警失败，请检查网络连接和权限"
            }

        # 格式化告警数据
        formatted_result = format_alert_data(alerts_data)

        logger.info(f"成功查询天眼告警，共获取到 {formatted_result['total']} 条重要告警")

        return {
            "status": "success",
            "data": formatted_result,
            "total_alerts": formatted_result['total'],
            "message": f"成功查询到 {formatted_result['total']} 条重要告警"
        }

    except Exception as e:
        logger.error(f"查询天眼告警出错: {str(e)}")
        return {
            "status": "error",
            "message": f"查询天眼告警出错: {str(e)}"
        }


if __name__ == "__main__":
    logger.info("启动奇安信天眼告警服务MCP服务器")
    mcp.run(transport="stdio")
