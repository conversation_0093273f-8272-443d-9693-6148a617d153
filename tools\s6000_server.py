from mcp.server.fastmcp import FastMCP
import logging
import json
from typing import List, Dict, Literal, Optional, Any
from pathlib import Path

# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 创建 FastMCP 实例
mcp = FastMCP("S60000业务流服务(缓存版本)")

# 缓存数据目录
CACHE_DIR = Path(__file__).parent.parent / "cache_data"

def load_cache_data(function_name: str) -> Optional[Dict[str, Any]]:
    """
    从缓存文件加载数据
    参数:
        function_name: 函数名称
    返回:
        缓存的数据或None
    """
    try:
        # 查找匹配的缓存文件
        for cache_file in CACHE_DIR.glob(f"{function_name}_*.json"):
            with open(cache_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if data.get('function_name') == function_name:
                    logger.info(f"从缓存文件 {cache_file.name} 加载数据")
                    return data.get('result')

        logger.warning(f"未找到函数 {function_name} 的缓存数据")
        return None
    except Exception as e:
        logger.error(f"加载缓存数据失败: {str(e)}")
        return None

@mcp.tool()
def get_pending_items(module_type: Literal["工作通知", "工作任务", "工作联系单", "预警单反馈", "全省预警通告"]) -> List[Dict[str, str]]:
    """从S60000系统的指定工作模块中获取待处理的条目列表（从缓存）。

    Args:
        module_type: 工作模块类型，可选值：工作通知、工作任务、工作联系单、预警单反馈、全省预警通告

    Returns:
        待处理条目列表，每个元素包含id、title、sender、received_date字段
    """
    logger.info(f"调用get_pending_items方法: module_type={module_type}（从缓存）")

    try:
        # 从缓存加载数据
        cache_data = load_cache_data("get_pending_items")

        if cache_data is None:
            logger.warning(f"未找到模块 {module_type} 的缓存数据，返回空列表")
            return []

        # 如果缓存数据是列表，直接返回
        if isinstance(cache_data, list):
            logger.info(f"成功从缓存获取 {len(cache_data)} 个待处理条目")
            return cache_data

        # 如果缓存数据是字典，尝试提取数据
        if isinstance(cache_data, dict):
            if "data" in cache_data:
                data = cache_data["data"]
                if isinstance(data, list):
                    logger.info(f"成功从缓存获取 {len(data)} 个待处理条目")
                    return data

        logger.warning("缓存数据格式不正确，返回空列表")
        return []

    except Exception as e:
        logger.error(f"获取待处理条目出错: {str(e)}")
        return []

if __name__ == "__main__":
    logger.info("启动S60000业务流服务MCP服务器")
    mcp.run(transport="stdio")
