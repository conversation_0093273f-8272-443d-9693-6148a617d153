#!/usr/bin/env python
# encoding:utf-8

"""
防火墙IP封禁器 MCP Server
基于 Model Context Protocol (MCP) 的防火墙IP封禁工具集成
提供2个主要功能：
1. 封禁IP地址
2. 查询封禁状态
"""

from mcp.server.fastmcp import FastMCP
import logging
import json
from typing import Dict, Optional, Any, List
from pathlib import Path

# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 创建 FastMCP 实例
mcp = FastMCP("防火墙IP封禁服务(缓存版本)")

# 缓存数据目录
CACHE_DIR = Path(__file__).parent.parent / "cache_data"

def load_cache_data(function_name: str) -> Optional[Dict[str, Any]]:
    """
    从缓存文件加载数据
    参数:
        function_name: 函数名称
    返回:
        缓存的数据或None
    """
    try:
        # 查找匹配的缓存文件
        for cache_file in CACHE_DIR.glob(f"{function_name}_*.json"):
            with open(cache_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if data.get('function_name') == function_name:
                    logger.info(f"从缓存文件 {cache_file.name} 加载数据")
                    return data.get('result')

        logger.warning(f"未找到函数 {function_name} 的缓存数据")
        return None
    except Exception as e:
        logger.error(f"加载缓存数据失败: {str(e)}")
        return None








@mcp.tool()
def block_ip_address(ip_address: str) -> Dict[str, Any]:
    """封禁指定的IP地址到防火墙封禁列表（从缓存）。

    Args:
        ip_address: 要封禁的IP地址，格式为IPv4地址（如：***********）

    Returns:
        Dict[str, Any]: 包含封禁操作结果的字典
            - status: "success" 或 "error"
            - data: 封禁操作详细信息（成功时）
            - message: 错误信息（失败时）
            - ip_address: 操作的IP地址
            - already_blocked: 是否已被封禁
            - total_blocked_ips: 总封禁IP数量
    """
    logger.info(f"[MCP工具] 开始封禁IP地址: {ip_address}（从缓存）")

    try:
        # 从缓存加载数据
        cache_data = load_cache_data("block_ip_address")

        if not cache_data:
            return {
                "status": "error",
                "message": "未找到IP封禁的缓存数据",
                "ip_address": ip_address
            }

        # 更新返回数据中的ip_address
        if isinstance(cache_data, dict):
            cache_data["ip_address"] = ip_address
            if "data" in cache_data and isinstance(cache_data["data"], dict):
                cache_data["data"]["ip_address"] = ip_address

        logger.info(f"成功从缓存获取IP封禁结果")
        return cache_data

    except Exception as e:
        logger.error(f"封禁IP地址出错: {str(e)}")
        return {
            "status": "error",
            "message": f"封禁IP地址出错: {str(e)}",
            "ip_address": ip_address
        }


@mcp.tool()
def query_block_status(ip_address: Optional[str] = None) -> Dict[str, Any]:
    """查询IP地址的封禁状态或获取所有已封禁的IP列表（从缓存）。

    Args:
        ip_address: 要查询状态的IP地址，留空则查询所有已封禁IP

    Returns:
        Dict[str, Any]: 包含查询结果的字典
            - status: "success" 或 "error"
            - data: 查询结果详细信息（成功时）
            - message: 错误信息（失败时）
            - query_ip: 查询的IP地址或"all"
            - is_blocked: 是否被封禁（查询单个IP时）
            - blocked_ips: 所有已封禁IP列表（查询全部时）
            - total_blocked_ips: 总封禁IP数量
    """
    logger.info(f"[MCP工具] 查询封禁状态: {ip_address if ip_address else '所有IP'}（从缓存）")

    try:
        # 从缓存加载数据
        cache_data = load_cache_data("query_block_status")

        if not cache_data:
            return {
                "status": "error",
                "message": "未找到封禁状态查询的缓存数据"
            }

        # 更新返回数据中的查询IP
        if isinstance(cache_data, dict) and ip_address:
            if "data" in cache_data and isinstance(cache_data["data"], dict):
                cache_data["data"]["query_ip"] = ip_address

        logger.info(f"成功从缓存获取封禁状态查询结果")
        return cache_data

    except Exception as e:
        logger.error(f"查询封禁状态出错: {str(e)}")
        return {
            "status": "error",
            "message": f"查询封禁状态出错: {str(e)}"
        }


if __name__ == "__main__":
    logger.info("启动防火墙IP封禁服务MCP服务器")
    mcp.run(transport="stdio")
