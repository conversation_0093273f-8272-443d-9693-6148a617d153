#!/usr/bin/env python
# encoding:utf-8

"""
绿盟漏洞扫描器 MCP Server (缓存版本)
基于 Model Context Protocol (MCP) 的绿盟漏洞扫描器工具集成
提供2个主要功能：
1. 获取所有扫描任务
2. 获取指定任务的漏洞信息

注意：此版本使用缓存数据，不访问真实系统
"""

from mcp.server.fastmcp import FastMCP
import logging
import json
import os
from typing import Dict, Optional, Any
from pathlib import Path

# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 创建 FastMCP 实例
mcp = FastMCP("绿盟漏洞扫描服务(缓存版本)")

# 缓存数据目录
CACHE_DIR = Path(__file__).parent.parent / "cache_data"


def load_cache_data(function_name: str) -> Optional[Dict[str, Any]]:
    """
    从缓存文件加载数据
    参数:
        function_name: 函数名称
    返回:
        缓存的数据或None
    """
    try:
        # 查找匹配的缓存文件
        for cache_file in CACHE_DIR.glob(f"{function_name}_*.json"):
            with open(cache_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if data.get('function_name') == function_name:
                    logger.info(f"从缓存文件 {cache_file.name} 加载数据")
                    return data.get('result')

        logger.warning(f"未找到函数 {function_name} 的缓存数据")
        return None
    except Exception as e:
        logger.error(f"加载缓存数据失败: {str(e)}")
        return None







@mcp.tool()
def get_all_scan_tasks() -> Dict[str, Any]:
    """获取绿盟漏洞扫描系统中的所有扫描任务。

    Returns:
        Dict[str, Any]: 包含所有扫描任务信息的字典
            - status: "success" 或 "error"
            - data: 简化的任务列表（成功时）
            - message: 错误信息（失败时）
            - total_tasks: 任务总数（成功时）
    """
    logger.info("[MCP工具] 开始获取所有扫描任务（从缓存）")

    try:
        # 从缓存加载数据
        cache_data = load_cache_data("get_all_scan_tasks")

        if not cache_data:
            return {
                "status": "error",
                "message": "未找到扫描任务的缓存数据"
            }

        logger.info(f"成功从缓存获取扫描任务数据")
        return cache_data

    except Exception as e:
        logger.error(f"获取所有任务出错: {str(e)}")
        return {
            "status": "error",
            "message": f"获取所有任务出错: {str(e)}"
        }








@mcp.tool()
def get_task_vulnerabilities(
    task_id: str
) -> Dict[str, Any]:
    """获取指定任务的所有漏洞信息（从缓存）。

    Args:
        task_id: 任务ID

    Returns:
        Dict[str, Any]: 包含漏洞信息的字典
            - status: "success" 或 "error"
            - data: 简化的漏洞列表（成功时）
            - message: 错误信息（失败时）
            - task_id: 任务ID
            - total_vulns: 漏洞总数（成功时）
            - vuln_level_count: 漏洞等级统计（成功时）
            - hosts_count: 受影响主机数量（成功时）
    """
    logger.info(f"[MCP工具] 开始获取任务 {task_id} 的漏洞信息（从缓存）")

    try:
        # 验证参数
        if not task_id or not task_id.strip():
            return {
                "status": "error",
                "message": "任务ID不能为空"
            }

        # 从缓存加载数据
        cache_data = load_cache_data("get_task_vulnerabilities")

        if not cache_data:
            return {
                "status": "error",
                "message": "未找到任务漏洞的缓存数据"
            }

        logger.info(f"成功从缓存获取任务 {task_id} 的漏洞信息")

        # 更新返回数据中的task_id
        if isinstance(cache_data, dict):
            cache_data["task_id"] = task_id

        return cache_data

    except Exception as e:
        logger.error(f"获取任务漏洞出错: {str(e)}")
        return {
            "status": "error",
            "message": f"获取任务漏洞出错: {str(e)}"
        }


if __name__ == "__main__":
    logger.info("启动绿盟漏洞扫描服务MCP服务器")
    mcp.run(transport="stdio")
