# MCP 工具集成系统

这是一个基于 Model Context Protocol (MCP) 的智能工具集成系统，提供了流式对话接口和自动工具发现功能。

## 🚀 项目概述

本项目实现了一个可扩展的 AI Agent 系统，能够：
- 自动发现和集成 MCP 工具
- 提供流式对话接口
- 维护对话上下文
- 支持多种业务工具集成

## 📁 项目结构

```
mcp/
├── main_server.py          # 主服务器，提供流式对话 API
├── test_client.py          # 测试客户端，演示对话流程
├── tools/                  # 工具目录
│   ├── email_server.py     # 邮件服务工具 (演示)
│   ├── s60000_server.py    # S60000业务流工具 (演示)
│   └── vulnscan_server.py  # 漏洞扫描工具 (演示)
└── README.md              # 本文档
```

## ⚠️ 重要说明

**当前 `tools/` 目录下的所有工具都是演示用的测试工具，返回的是模拟数据，不连接真实系统。**

这些演示工具的目的是：
- 展示 MCP 工具的基本结构
- 演示工具集成流程
- 提供开发参考模板

## 🛠️ 环境要求

- Python 3.8+
- Ollama (本地运行，端口 11444)
- 所需 Python 包：
  ```bash
  pip install fastapi uvicorn langchain-ollama langchain-mcp-adapters langgraph
  ```

## 🚀 快速开始

### 1. 启动主服务器

```bash
python main_server.py
```

服务器将在 `http://localhost:28123` 启动，并自动发现 `tools/` 目录下的所有工具。

### 2. 运行测试客户端

```bash
python test_client.py
```

测试客户端会自动执行一系列对话，演示工具调用功能。

### 3. 手动测试 API

使用 curl 或其他 HTTP 客户端：

```bash
curl -X POST "http://localhost:28123/chat" \
     -H "Content-Type: application/json" \
     -d '{"message": "检查一下有没有新邮件"}'
```

## 📋 当前演示工具

### 1. 邮件服务 (email_server.py)
- **功能**: 检查新邮件
- **工具方法**: `check_new_mail()`
- **返回**: 模拟的邮件列表

### 2. S60000业务流服务 (s60000_server.py)
- **功能**: 获取业务流待处理事项
- **工具方法**: `get_pending_items(module_type)`
- **支持模块**: 工作通知、工作任务、工作联系单、预警单反馈、全省预警通告

### 3. 漏洞扫描服务 (vulnscan_server.py)
- **功能**: 查询扫描任务状态
- **工具方法**: `list_tasks()`
- **返回**: 模拟的扫描任务列表

## 🔧 开发指南：如何添加新工具

### 步骤 1: 创建工具服务器文件

在 `tools/` 目录下创建新的工具文件，命名格式为 `{工具名}_server.py`：

```python
from mcp.server.fastmcp import FastMCP
import logging
from typing import List, Dict, Optional, Literal

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建 FastMCP 实例
mcp = FastMCP("你的工具名称")

@mcp.tool()
def your_tool_function(
    param1: str,
    param2: Optional[int] = None,
    operation: Literal["create", "read", "update", "delete"] = "read"
) -> Dict[str, str]:
    """工具功能描述

    Args:
        param1: 必需参数描述
        param2: 可选参数描述
        operation: 操作类型

    Returns:
        标准化返回格式
    """
    logger.info(f"调用工具: {param1}, {param2}, {operation}")

    try:
        # 在这里实现你的业务逻辑
        # 连接真实系统、调用 API、处理数据等

        return {
            "status": "success",
            "data": "你的返回数据",
            "message": "操作完成"
        }
    except Exception as e:
        logger.error(f"工具执行失败: {e}")
        return {
            "status": "error",
            "message": str(e)
        }

if __name__ == "__main__":
    mcp.run()
```

### 步骤 2: 工具自动发现

系统会自动发现新工具，无需修改主服务器代码。`main_server.py` 中的 `discover_tools()` 函数会：

1. 扫描 `tools/` 目录
2. 查找所有 `*_server.py` 文件
3. 自动注册为可用工具

### 步骤 3: 测试新工具

1. 重启主服务器：
   ```bash
   python main_server.py
   ```

2. 在对话中测试新工具：
   ```bash
   curl -X POST "http://localhost:28123/chat" \
        -H "Content-Type: application/json" \
        -d '{"message": "使用我的新工具做某某事"}'
   ```

### 步骤 4: 工具开发最佳实践

#### 4.1 函数签名规范
```python
from typing import Optional, Literal, Dict, List, Any

@mcp.tool()
def standard_function(
    required_param: str,
    optional_param: Optional[int] = None,
    choice_param: Literal["option1", "option2", "option3"] = "option1"
) -> Dict[str, Any]:
    """简洁明了的函数描述。

    Args:
        required_param: 必需参数说明
        optional_param: 可选参数说明，默认None
        choice_param: 选择参数说明，限定值范围

    Returns:
        标准化返回格式：{"status": "success/error", "data": ..., "message": "..."}
    """
    # 实现逻辑
    pass
```

#### 4.2 错误处理
```python
@mcp.tool()
def robust_function(param: str) -> Dict[str, Any]:
    """带错误处理的工具函数"""
    try:
        # 输入验证
        if not param:
            return {"status": "error", "message": "参数不能为空"}

        # 业务逻辑
        result = some_business_logic(param)

        return {"status": "success", "data": result}

    except ValueError as e:
        logger.error(f"参数错误: {e}")
        return {"status": "error", "message": f"参数错误: {e}"}
    except Exception as e:
        logger.error(f"执行失败: {e}")
        return {"status": "error", "message": f"执行失败: {e}"}
```

#### 4.3 日志记录
```python
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@mcp.tool()
def logged_function(param: str) -> Dict[str, Any]:
    """带日志记录的工具函数"""
    logger.info(f"开始执行: param={param}")

    try:
        # 业务逻辑
        result = process_data(param)
        logger.info("执行成功")
        return {"status": "success", "data": result}

    except Exception as e:
        logger.error(f"执行失败: {e}")
        return {"status": "error", "message": str(e)}
```

#### 4.4 配置管理
```python
import os
from typing import Dict, Any

# 从环境变量读取配置
CONFIG = {
    "api_url": os.getenv("API_URL", "http://localhost:8080"),
    "api_key": os.getenv("API_KEY", ""),
    "timeout": int(os.getenv("TIMEOUT", "30")),
    "debug": os.getenv("DEBUG", "false").lower() == "true"
}

@mcp.tool()
def configured_function(data: str) -> Dict[str, Any]:
    """使用配置的工具函数"""
    if not CONFIG["api_key"]:
        return {"status": "error", "message": "API密钥未配置"}

    # 使用配置进行处理
    timeout = CONFIG["timeout"]
    # ... 业务逻辑

    return {"status": "success", "data": "处理完成"}
```


### 步骤 5: 集成真实系统

#### 5.1 数据库集成
```python
import sqlite3
from contextlib import contextmanager
from typing import Optional, List, Dict, Any, Generator

@contextmanager
def get_db_connection() -> Generator[sqlite3.Connection, None, None]:
    """获取数据库连接的上下文管理器。

    自动管理数据库连接的生命周期，确保连接在使用后正确关闭，
    避免资源泄漏和连接池耗尽问题。

    Yields:
        sqlite3.Connection: 数据库连接对象
    """
    conn = sqlite3.connect("your_database.db")
    try:
        yield conn
    finally:
        conn.close()

@mcp.tool()
def query_database(sql: str, params: Optional[List] = None) -> Dict[str, Any]:
    """执行安全的数据库查询操作。

    使用参数化查询防止SQL注入攻击，自动处理连接管理和异常处理，
    返回标准化的结果格式便于上层应用处理。

    Args:
        sql: SQL查询语句，支持参数化查询（使用?占位符）
        params: SQL参数列表，与查询语句中的占位符对应，默认为None

    Returns:
        Dict[str, Any]: 标准化返回格式
            - status: "success" 表示查询成功，"error" 表示查询失败
            - data: 查询结果列表，每行数据为字典格式（列名:值）
            - message: 错误信息（仅在失败时返回）
    """
    try:
        # 输入验证
        if not sql or not sql.strip():
            return {"status": "error", "message": "SQL语句不能为空"}

        with get_db_connection() as conn:
            cursor = conn.execute(sql, params or [])

            # 获取列名信息
            columns = [desc[0] for desc in cursor.description]

            # 将查询结果转换为字典列表
            rows = [dict(zip(columns, row)) for row in cursor.fetchall()]

            logger.info(f"数据库查询成功，返回 {len(rows)} 条记录")
            return {"status": "success", "data": rows}

    except sqlite3.Error as e:
        logger.error(f"数据库操作失败: {e}")
        return {"status": "error", "message": f"数据库操作失败: {e}"}
    except Exception as e:
        logger.error(f"数据库查询失败: {e}")
        return {"status": "error", "message": str(e)}
```

#### 5.2 REST API 集成
```python
import requests
from typing import Optional, Dict, Any, Literal

@mcp.tool()
def call_external_api(
    endpoint: str,
    method: Literal["GET", "POST", "PUT", "DELETE", "PATCH"] = "GET",
    data: Optional[Dict[str, Any]] = None,
    headers: Optional[Dict[str, str]] = None,
    timeout: Optional[int] = None
) -> Dict[str, Any]:
    """调用外部REST API接口。

    提供统一的外部API调用接口，支持多种HTTP方法，自动处理认证、
    超时、错误重试等常见场景，返回标准化的响应格式。

    Args:
        endpoint: API端点路径，相对于配置的基础URL
        method: HTTP请求方法，支持GET/POST/PUT/DELETE/PATCH
        data: 请求体数据，将自动序列化为JSON格式
        headers: 额外的请求头，会与默认认证头合并
        timeout: 请求超时时间（秒），默认使用配置中的值

    Returns:
        Dict[str, Any]: 标准化返回格式
            - status: "success" 表示调用成功，"error" 表示调用失败
            - data: API响应数据（JSON格式）
            - message: 错误信息（仅在失败时返回）
            - status_code: HTTP状态码（仅在成功时返回）
    """
    try:
        # 输入验证
        if not endpoint:
            return {"status": "error", "message": "API端点不能为空"}

        if not CONFIG.get('api_url'):
            return {"status": "error", "message": "API基础URL未配置"}

        # 构建完整URL
        url = f"{CONFIG['api_url'].rstrip('/')}/{endpoint.lstrip('/')}"

        # 准备请求头
        request_headers = {"Content-Type": "application/json"}

        # 添加认证头（如果配置了API密钥）
        if CONFIG.get('api_key'):
            request_headers["Authorization"] = f"Bearer {CONFIG['api_key']}"

        # 合并自定义请求头
        if headers:
            request_headers.update(headers)

        # 确定超时时间
        request_timeout = timeout or CONFIG.get('timeout', 30)

        logger.info(f"调用外部API: {method} {url}")

        # 发送HTTP请求
        response = requests.request(
            method=method.upper(),
            url=url,
            json=data,
            headers=request_headers,
            timeout=request_timeout
        )

        # 检查HTTP状态码
        response.raise_for_status()

        # 解析响应数据
        try:
            response_data = response.json()
        except ValueError:
            # 如果响应不是JSON格式，返回原始文本
            response_data = response.text

        logger.info(f"API调用成功，状态码: {response.status_code}")
        return {
            "status": "success",
            "data": response_data,
            "status_code": response.status_code
        }

    except requests.exceptions.Timeout as e:
        logger.error(f"API调用超时: {e}")
        return {"status": "error", "message": f"请求超时（{request_timeout}秒）"}

    except requests.exceptions.ConnectionError as e:
        logger.error(f"API连接失败: {e}")
        return {"status": "error", "message": "无法连接到API服务器"}

    except requests.exceptions.HTTPError as e:
        logger.error(f"API返回错误状态码: {e}")
        return {
            "status": "error",
            "message": f"HTTP错误: {e.response.status_code} - {e.response.reason}"
        }

    except requests.RequestException as e:
        logger.error(f"API调用失败: {e}")
        return {"status": "error", "message": f"请求失败: {str(e)}"}

    except Exception as e:
        logger.error(f"API调用异常: {e}")
        return {"status": "error", "message": f"未知错误: {str(e)}"}
```

## 🔍 调试和监控

### 查看工具发现日志
启动服务器时会显示发现的工具：
```
发现工具: email -> ./tools/email_server.py
发现工具: s60000 -> ./tools/s60000_server.py
发现工具: vulnscan -> ./tools/vulnscan_server.py
发现工具: your_new_tool -> ./tools/your_new_tool_server.py
```

### 查看工具调用日志
每次工具调用都会在控制台显示详细信息：
```
[工具调用]: 正在使用工具 'check_new_mail'，输入: {}...
[工具结果]: 'check_new_mail' 返回: [{"from": "...", "subject": "..."}]
```

## 📚 相关资源

- [Model Context Protocol 官方文档](https://modelcontextprotocol.io/)
- [FastMCP 文档](https://github.com/jlowin/fastmcp)
- [LangChain MCP 适配器](https://github.com/langchain-ai/langchain-mcp-adapters)

