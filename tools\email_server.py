from mcp.server.fastmcp import FastMCP
import logging
import json
import os
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime
from pathlib import Path

# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 创建 FastMCP 实例
mcp = FastMCP("邮件服务(缓存版本)")

# 缓存数据目录
CACHE_DIR = Path(__file__).parent.parent / "cache_data"

def load_cache_data(function_name: str) -> Optional[Dict[str, Any]]:
    """
    从缓存文件加载数据
    参数:
        function_name: 函数名称
    返回:
        缓存的数据或None
    """
    try:
        # 查找匹配的缓存文件
        for cache_file in CACHE_DIR.glob(f"{function_name}_*.json"):
            with open(cache_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if data.get('function_name') == function_name:
                    logger.info(f"从缓存文件 {cache_file.name} 加载数据")
                    return data.get('result')

        logger.warning(f"未找到函数 {function_name} 的缓存数据")
        return None
    except Exception as e:
        logger.error(f"加载缓存数据失败: {str(e)}")
        return None






@mcp.tool()
def check_new_mail() -> Tuple[List[Dict[str, Any]], int]:
    """收取到新邮件时，发起通知。此工具用于检查邮箱并返回新收到的未读邮件列表（从缓存）。

    """
    logger.info("调用check_new_mail方法，检查新邮件（从缓存）")

    try:
        # 从缓存加载数据
        cache_data = load_cache_data("check_new_mail")

        if not cache_data:
            logger.warning("未找到邮件检查的缓存数据")
            return [], 0

        # 解析缓存数据
        if isinstance(cache_data, list) and len(cache_data) == 2:
            all_unread, total_unread = cache_data
            logger.info(f"成功从缓存获取邮件数据，共 {total_unread} 封未读邮件")
            return all_unread, total_unread
        else:
            logger.warning("缓存数据格式不正确")
            return [], 0

    except Exception as e:
        logger.error(f"获取邮件缓存数据失败: {str(e)}")
        return [], 0

if __name__ == "__main__":
    # logger.info("========== 开始测试邮件读取功能 ==========")
    
    # # 调用核心函数进行测试
    # try:
    #     all_unread_emails, total_count = check_new_mail()
        
    #     logger.info(f"测试完成，共发现 {total_count} 封新的未读邮件。")
        
    #     if all_unread_emails:
    #         logger.info("邮件详情如下：")
    #         for i, email_data in enumerate(all_unread_emails, 1):
    #             print("-" * 50)
    #             print(f"邮件 #{i}")
    #             print(f"  > 账户: {email_data.get('account')}")
    #             print(f"  > 发件人: {email_data.get('from')}")
    #             print(f"  > 主题: {email_data.get('subject')}")
    #             print(f"  > 日期: {email_data.get('date')}")
                
    #             text_body = email_data.get('body', {}).get('text', '无文本正文').strip()
    #             preview = (text_body[:120] + '...') if len(text_body) > 120 else text_body
    #             print(f"  > 正文预览: {preview}")
                
    #             attachments = email_data.get('attachments', [])
    #             if attachments:
    #                 print(f"  > 附件 ({len(attachments)}个):")
    #                 for att in attachments:
    #                     att_info = f"{att.get('filename')} ({att.get('size')} bytes)"
    #                     if att.get('message'):
    #                         att_info += f" - {att.get('message')}"
    #                     print(f"    - {att_info}")
    #             else:
    #                 print("  > 附件: 无")
    #         print("-" * 50)
    #     else:
    #         logger.info("所有账户均无新的未读邮件。")
    # except Exception as e:
    #     logger.error(f"邮件读取测试过程中发生错误: {e}", exc_info=True)

    # logger.info("========== 邮件读取功能测试结束 ==========")
    
    logger.info("启动邮件服务MCP服务器")
    mcp.run(transport="stdio")